/**
 * 权限策略服务测试文件
 * 用于验证修复后的服务是否正常工作
 */
import { permissionPolicyService, PolicyType, ConditionType } from './PermissionPolicyService';
import { Permission } from './PermissionService';
import { CollaborationRole } from './CollaborationService';

// 测试权限策略服务
function testPermissionPolicyService() {
  console.log('开始测试权限策略服务...');

  try {
    // 测试创建用户策略
    const userPolicy = permissionPolicyService.createPolicy({
      name: '测试用户策略',
      description: '用于测试的用户策略',
      type: PolicyType.USER,
      enabled: true,
      priority: 100,
      permissions: [Permission.SCENE_EDIT],
      userIds: ['user123'],
    });

    console.log('✅ 用户策略创建成功:', userPolicy.id);

    // 测试创建时间策略
    const timePolicy = permissionPolicyService.createPolicy({
      name: '测试时间策略',
      description: '工作时间权限策略',
      type: PolicyType.TIME,
      enabled: true,
      priority: 50,
      permissions: [Permission.SCENE_EDIT, Permission.ENTITY_CREATE],
      timeRestrictions: {
        daysOfWeek: [1, 2, 3, 4, 5], // 周一到周五
        hours: [9, 10, 11, 12, 13, 14, 15, 16, 17], // 9:00-17:00
      },
    });

    console.log('✅ 时间策略创建成功:', timePolicy.id);

    // 测试权限评估
    const hasPermission = permissionPolicyService.evaluatePermission(
      'user123',
      Permission.SCENE_EDIT
    );

    console.log('✅ 权限评估结果:', hasPermission);

    // 测试获取所有策略
    const allPolicies = permissionPolicyService.getAllPolicies();
    console.log('✅ 获取所有策略数量:', allPolicies.length);

    // 测试按类型获取策略
    const userPolicies = permissionPolicyService.getPoliciesByType(PolicyType.USER);
    console.log('✅ 用户策略数量:', userPolicies.length);

    // 测试更新策略
    const updatedPolicy = permissionPolicyService.updatePolicy(userPolicy.id, {
      description: '更新后的用户策略描述',
      priority: 200,
    });

    console.log('✅ 策略更新成功:', updatedPolicy?.version);

    // 测试应用策略
    const applyResult = permissionPolicyService.applyPolicy(
      userPolicy.id,
      'target123',
      'entity'
    );

    console.log('✅ 策略应用结果:', applyResult);

    // 测试角色策略
    const rolePolicy = permissionPolicyService.createPolicy({
      name: '测试角色策略',
      description: '仅允许管理员编辑',
      type: PolicyType.ROLE,
      enabled: true,
      priority: 150,
      permissions: [Permission.SCENE_EDIT],
      roles: [CollaborationRole.ADMIN, CollaborationRole.OWNER],
    });

    console.log('✅ 角色策略创建成功:', rolePolicy.id);

    // 测试组织策略
    const orgPolicy = permissionPolicyService.createPolicy({
      name: '测试组织策略',
      description: '特定组织权限',
      type: PolicyType.ORGANIZATION,
      enabled: true,
      priority: 120,
      permissions: [Permission.ENTITY_CREATE],
      organizationIds: ['org_123', 'org_456'],
    });

    console.log('✅ 组织策略创建成功:', orgPolicy.id);

    // 测试条件策略
    const conditionPolicy = permissionPolicyService.createPolicy({
      name: '测试条件策略',
      description: '基于条件的权限控制',
      type: PolicyType.CONDITION,
      enabled: true,
      priority: 80,
      permissions: [Permission.SCENE_EDIT],
      conditions: [
        {
          type: ConditionType.EQUALS,
          value: 'development'
        },
        {
          type: ConditionType.GREATER_THAN,
          value: 5
        }
      ],
    });

    console.log('✅ 条件策略创建成功:', conditionPolicy.id);

    // 测试资源策略
    const resourcePolicy = permissionPolicyService.createPolicy({
      name: '测试资源策略',
      description: '特定资源权限',
      type: PolicyType.RESOURCE,
      enabled: true,
      priority: 90,
      permissions: [Permission.ENTITY_DELETE],
      resourceTypes: ['entity', 'component'],
      resourceIds: ['entity_123', 'component_456'],
    });

    console.log('✅ 资源策略创建成功:', resourcePolicy.id);

    // 测试带上下文的权限评估
    const contextPermissionResult = permissionPolicyService.evaluatePermission(
      'user123',
      Permission.ENTITY_DELETE,
      {
        resourceType: 'entity',
        resourceId: 'entity_123',
        environment: 'development',
        userLevel: 10
      }
    );

    console.log('✅ 带上下文的权限评估结果:', contextPermissionResult);

    // 测试条件策略评估
    const conditionPermissionResult = permissionPolicyService.evaluatePermission(
      'user123',
      Permission.SCENE_EDIT,
      {
        environment: 'development',
        userLevel: 10
      }
    );

    console.log('✅ 条件策略权限评估结果:', conditionPermissionResult);

    // 测试获取用户适用策略
    const userPolicies = permissionPolicyService.getUserApplicablePolicies('user123', Permission.SCENE_EDIT);
    console.log('✅ 用户适用策略数量:', userPolicies.length);

    // 测试策略冲突检查
    const conflictReport = permissionPolicyService.checkPolicyConflicts();
    console.log('✅ 策略冲突检查:', conflictReport.hasConflicts ? `发现 ${conflictReport.conflicts.length} 个冲突` : '无冲突');

    // 测试策略统计信息
    const statistics = permissionPolicyService.getPolicyStatistics();
    console.log('✅ 策略统计信息:', {
      总数: statistics.total,
      启用: statistics.enabled,
      禁用: statistics.disabled,
      平均优先级: statistics.averagePriority.toFixed(2)
    });

    // 测试删除策略
    const deleteResult = permissionPolicyService.deletePolicy(timePolicy.id);
    console.log('✅ 策略删除结果:', deleteResult);

    console.log('🎉 权限策略服务测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 导出测试函数
export { testPermissionPolicyService };

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  testPermissionPolicyService();
}
