/**
 * 权限策略服务测试文件
 * 用于验证修复后的服务是否正常工作
 */
import { permissionPolicyService, PolicyType } from './PermissionPolicyService';
import { Permission } from './PermissionService';
import { CollaborationRole } from './CollaborationService';

// 测试权限策略服务
function testPermissionPolicyService() {
  console.log('开始测试权限策略服务...');

  try {
    // 测试创建用户策略
    const userPolicy = permissionPolicyService.createPolicy({
      name: '测试用户策略',
      description: '用于测试的用户策略',
      type: PolicyType.USER,
      enabled: true,
      priority: 100,
      permissions: [Permission.SCENE_EDIT],
      userIds: ['user123'],
    });

    console.log('✅ 用户策略创建成功:', userPolicy.id);

    // 测试创建时间策略
    const timePolicy = permissionPolicyService.createPolicy({
      name: '测试时间策略',
      description: '工作时间权限策略',
      type: PolicyType.TIME,
      enabled: true,
      priority: 50,
      permissions: [Permission.SCENE_EDIT, Permission.ENTITY_CREATE],
      timeRestrictions: {
        daysOfWeek: [1, 2, 3, 4, 5], // 周一到周五
        hours: [9, 10, 11, 12, 13, 14, 15, 16, 17], // 9:00-17:00
      },
    });

    console.log('✅ 时间策略创建成功:', timePolicy.id);

    // 测试权限评估
    const hasPermission = permissionPolicyService.evaluatePermission(
      'user123',
      Permission.SCENE_EDIT
    );

    console.log('✅ 权限评估结果:', hasPermission);

    // 测试获取所有策略
    const allPolicies = permissionPolicyService.getAllPolicies();
    console.log('✅ 获取所有策略数量:', allPolicies.length);

    // 测试按类型获取策略
    const userPolicies = permissionPolicyService.getPoliciesByType(PolicyType.USER);
    console.log('✅ 用户策略数量:', userPolicies.length);

    // 测试更新策略
    const updatedPolicy = permissionPolicyService.updatePolicy(userPolicy.id, {
      description: '更新后的用户策略描述',
      priority: 200,
    });

    console.log('✅ 策略更新成功:', updatedPolicy?.version);

    // 测试应用策略
    const applyResult = permissionPolicyService.applyPolicy(
      userPolicy.id,
      'target123',
      'entity'
    );

    console.log('✅ 策略应用结果:', applyResult);

    // 测试删除策略
    const deleteResult = permissionPolicyService.deletePolicy(timePolicy.id);
    console.log('✅ 策略删除结果:', deleteResult);

    console.log('🎉 权限策略服务测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 导出测试函数
export { testPermissionPolicyService };

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  testPermissionPolicyService();
}
